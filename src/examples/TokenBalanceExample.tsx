/**
 * 代币余额查询功能使用示例
 * 展示如何使用新添加的批量余额查询功能
 */

import { observer } from 'mobx-react-lite';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { type Address } from 'viem';
import { useStore } from '@/store';
import { useTokenBalances } from '@/hooks/useTokenBalances';
import TokenBalanceList from '@/components/TokenBalanceList';

const TokenBalanceExample = observer(() => {
  const { tokenStore } = useStore();
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  // 使用批量余额查询 Hook
  const {
    isLoading,
    error,
    refetch,
    isRefetching,
    tokensCount,
  } = useTokenBalances(
    address as Address,
    typeof chainId === 'number' ? chainId : undefined,
    isConnected && !!chainId,
    30000 // 30秒自动刷新
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">代币余额查询示例</h1>
      
      {/* 连接状态 */}
      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">连接状态</h2>
        <p>钱包地址: {address || '未连接'}</p>
        <p>当前网络: {chainId || '未选择'}</p>
        <p>代币数量: {tokensCount}</p>
      </div>

      {/* 查询状态 */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">查询状态</h2>
        <p>加载中: {isLoading ? '是' : '否'}</p>
        <p>刷新中: {isRefetching ? '是' : '否'}</p>
        {error && (
          <p className="text-red-600">错误: {error.message}</p>
        )}
        <button
          onClick={() => refetch()}
          disabled={isLoading || isRefetching}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          手动刷新
        </button>
      </div>

      {/* 代币列表 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-4">代币余额列表</h2>
        {isConnected ? (
          <TokenBalanceList />
        ) : (
          <p className="text-gray-500">请先连接钱包</p>
        )}
      </div>

      {/* 原始数据展示 */}
      <div className="p-4 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">原始数据 (调试用)</h2>
        <pre className="text-sm overflow-auto">
          {JSON.stringify(
            {
              tokenCount: tokenStore.tokenList.length,
              tokensOnCurrentChain: tokenStore.getTokensOnChain(typeof chainId === 'number' ? chainId : 0).length,
              sampleTokens: tokenStore.tokenList.slice(0, 3).map(token => ({
                symbol: token.symbol,
                address: token.address,
                balance: token.balance,
                balanceLoading: token.balanceLoading,
                formattedBalance: token.formattedBalance,
              })),
            },
            null,
            2
          )}
        </pre>
      </div>
    </div>
  );
});

export default TokenBalanceExample;
