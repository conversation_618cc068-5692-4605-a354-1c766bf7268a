import { makeAutoObservable } from "mobx";
import axios from "axios";
import Token from "@/types/token";
import { formatUnits } from "viem";

export class TokenStore {
  private readonly CACHE_DURATION = 3600000;
  private readonly CACHE_KEY = "token_list_cache";
  private readonly CACHE_TIMESTAMP_KEY = "token_list_cache_timestamp";

  tokenList: Token[] = [];

  constructor() {
    makeAutoObservable(this);
  }

  async getTokenList(chainId: number, destChainId: number) {
    const now = Date.now();
    const cacheKey = `${this.CACHE_KEY}_${chainId}_${destChainId}`;
    const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;

    const cachedData = localStorage.getItem(cacheKey);
    const cachedTimestamp = localStorage.getItem(timestampKey);

    if (cachedData && cachedTimestamp) {
      const timestamp = parseInt(cachedTimestamp, 10);
      if (now - timestamp < this.CACHE_DURATION) {
        try {
          const cachedTokens = JSON.parse(cachedData);
          // 将缓存的普通对象转换为 Token 实例
          this.tokenList = cachedTokens.map((tokenData: any) => new Token(tokenData));
        } catch (error) {
          localStorage.removeItem(cacheKey);
          localStorage.removeItem(timestampKey);
        }
      }
    }

    try {
      const response = await axios.get(
        `http://localhost:9527/tube/getTokenList`,
        {
          params: {
            chainId,
            destChainId,
          },
        },
      );

      const tokens = response.data || [];

      if (tokens.length > 0) {
        localStorage.setItem(cacheKey, JSON.stringify(tokens));
        localStorage.setItem(timestampKey, now.toString());
      }

      // 将普通对象转换为 Token 实例
      this.tokenList = tokens.map((tokenData: any) => new Token(tokenData));
    } catch (error) {
      console.error("Failed to fetch token list:", error);
      if (cachedData) {
        try {
          const cachedTokens = JSON.parse(cachedData);
          // 将缓存的普通对象转换为 Token 实例
          this.tokenList = cachedTokens.map((tokenData: any) => new Token(tokenData));
        } catch (parseError) {
          console.error(
            "Failed to parse cached token list as fallback:",
            parseError,
          );
          this.tokenList = [];
        }
      } else {
        this.tokenList = [];
      }
    }
  }

  /**
   * 清除缓存
   * @param chainId 可选，指定清除特定链的缓存
   * @param destChainId 可选，指定清除特定目标链的缓存
   */
  clearCache(chainId?: number, destChainId?: number): void {
    if (chainId !== undefined && destChainId !== undefined) {
      // 清除特定链的缓存
      const cacheKey = `${this.CACHE_KEY}_${chainId}_${destChainId}`;
      const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;
      localStorage.removeItem(cacheKey);
      localStorage.removeItem(timestampKey);
    } else {
      // 清除所有相关缓存
      const keys = Object.keys(localStorage);
      keys.forEach((key) => {
        if (
          key.startsWith(this.CACHE_KEY) ||
          key.startsWith(this.CACHE_TIMESTAMP_KEY)
        ) {
          localStorage.removeItem(key);
        }
      });
    }
  }

  /**
   * 检查缓存是否有效
   * @param chainId 源链ID
   * @param destChainId 目标链ID
   */
  isCacheValid(chainId: number = 4689, destChainId: number = 1): boolean {
    const now = Date.now();
    const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;
    const cachedTimestamp = localStorage.getItem(timestampKey);

    if (!cachedTimestamp) {
      return false;
    }

    const timestamp = parseInt(cachedTimestamp, 10);
    return now - timestamp < this.CACHE_DURATION;
  }

  /**
   * 获取指定链上的代币列表
   * @param chainId 链ID
   * @returns 该链上的代币数组
   */
  getTokensOnChain(chainId: number): Token[] {
    return this.tokenList.filter(
      (token) => token.chainid === chainId && token.address
    );
  }

  /**
   * 更新代币余额的方法
   * @param tokenAddress 代币地址
   * @param balance 余额（wei 单位）
   * @param decimals 代币精度
   */
  updateTokenBalance(tokenAddress: string, balance: bigint, decimals: number): void {
    const token = this.tokenList.find((t) => t.address === tokenAddress);
    if (token) {
      const formattedBalance = formatUnits(balance, decimals);
      token.setBalance(formattedBalance);
      token.setBalanceLoading(false);
    }
  }

  /**
   * 批量更新代币余额
   * @param results 批量查询的结果数组
   * @param tokens 对应的代币数组
   */
  updateBatchBalances(results: any[], tokens: Token[]): void {
    results.forEach((result, index) => {
      const token = tokens[index];
      if (token && result.status === 'success' && result.result) {
        const decimals = token.decimals || 18;
        const formattedBalance = formatUnits(result.result as bigint, decimals);
        token.setBalance(formattedBalance);
      }
      if (token) {
        token.setBalanceLoading(false);
      }
    });
  }
}
