import { observer } from 'mobx-react-lite';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { type Address } from 'viem';
import { useStore } from '@/store';
import { useTokenBalances } from '@/hooks/useTokenBalances';
import { Image, Spinner } from '@heroui/react';
import Token from '@/types/token';

/**
 * 代币余额列表组件
 * 展示当前链上所有代币的余额
 */
const TokenBalanceList = observer(() => {
  const { tokenStore } = useStore();
  const { address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  // 使用自定义 Hook 批量查询余额
  const {
    isLoading,
    error,
    refetch,
    isRefetching,
    tokensCount,
  } = useTokenBalances(
    address as Address,
    typeof chainId === 'number' ? chainId : undefined,
    !!address && !!chainId, // 只有在有地址和链ID时才启用
    30000 // 30秒自动刷新
  );

  // 获取当前链上的代币
  const tokensOnChain = tokenStore.getTokensOnChain(typeof chainId === 'number' ? chainId : 0);

  if (!address) {
    return (
      <div className="text-center py-8">
        <p className="text-color7">请先连接钱包</p>
      </div>
    );
  }

  if (!chainId) {
    return (
      <div className="text-center py-8">
        <p className="text-color7">请选择网络</p>
      </div>
    );
  }

  if (tokensOnChain.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-color7">当前网络暂无可用代币</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-color8">
          代币余额 ({tokensCount})
        </h3>
        <button
          onClick={() => refetch()}
          disabled={isLoading || isRefetching}
          className="px-3 py-1 text-sm bg-color3 text-color8 rounded-md hover:bg-opacity-80 disabled:opacity-50"
        >
          {isRefetching ? '刷新中...' : '刷新'}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
          <p className="text-red-700 text-sm">
            查询余额失败: {error.message}
          </p>
        </div>
      )}

      <div className="space-y-2">
        {tokensOnChain.map((token) => (
          <TokenBalanceItem key={token.address} token={token} />
        ))}
      </div>
    </div>
  );
});

/**
 * 单个代币余额项组件
 */
const TokenBalanceItem = observer(({ token }: { token: Token }) => {
  return (
    <div className="flex items-center justify-between p-3 bg-color6 border border-border rounded-lg">
      <div className="flex items-center space-x-3">
        <Image
          src={token.logouri}
          alt={token.symbol}
          className="w-8 h-8 rounded-full"
          fallbackSrc="/images/default-token.svg"
        />
        <div>
          <p className="font-medium text-color8">{token.symbol}</p>
          <p className="text-sm text-color7">{token.name}</p>
        </div>
      </div>
      
      <div className="text-right">
        {token.balanceLoading ? (
          <div className="flex items-center space-x-2">
            <Spinner size="sm" />
            <span className="text-sm text-color7">加载中...</span>
          </div>
        ) : (
          <div>
            <p className="font-medium text-color8">
              {token.formattedBalance}
            </p>
            <p className="text-sm text-color7">{token.symbol}</p>
          </div>
        )}
      </div>
    </div>
  );
});

TokenBalanceItem.displayName = 'TokenBalanceItem';

export default TokenBalanceList;
