import { useEffect } from 'react';
import { useReadContracts } from 'wagmi';
import { type Address } from 'viem';
import { ERC20_ABI } from '@/constants/abi';
import { useStore } from '@/store';

/**
 * 自定义 Hook 用于批量查询代币余额
 * @param userAddress 用户钱包地址
 * @param chainId 当前链ID
 * @param enabled 是否启用查询
 * @param refetchInterval 自动刷新间隔（毫秒）
 */
export function useTokenBalances(
  userAddress?: Address,
  chainId?: number,
  enabled: boolean = true,
  refetchInterval: number = 30000
) {
  const { tokenStore } = useStore();

  // 过滤出当前链上有地址的代币
  const tokensOnChain = tokenStore.tokenList.filter(
    (token) => token.chainid === chainId && token.address
  );

  // 构建批量合约调用配置
  const contracts = tokensOnChain.map((token) => ({
    address: token.address as Address,
    abi: ERC20_ABI,
    functionName: 'balanceOf' as const,
    args: [userAddress!],
  }));

  // 使用 wagmi 的 useReadContracts 进行批量查询
  const {
    data: balanceResults,
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useReadContracts({
    contracts,
    query: {
      enabled: enabled && !!userAddress && contracts.length > 0,
      refetchInterval,
      staleTime: 30000, // 30秒内认为数据新鲜
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  });

  // 当查询结果返回时，更新 tokenStore 中的余额
  useEffect(() => {
    if (balanceResults && tokensOnChain.length > 0) {
      tokenStore.updateBatchBalances(balanceResults, tokensOnChain);
    }
  }, [balanceResults, tokensOnChain, tokenStore]);

  // 设置加载状态
  useEffect(() => {
    if (tokensOnChain.length > 0) {
      tokensOnChain.forEach((token) => {
        token.setBalanceLoading(isLoading || isRefetching);
      });
    }
  }, [isLoading, isRefetching, tokensOnChain]);

  return {
    balanceResults,
    isLoading,
    error,
    refetch,
    isRefetching,
    tokensCount: tokensOnChain.length,
  };
}

/**
 * 自定义 Hook 用于查询单个代币的余额
 * @param tokenAddress 代币合约地址
 * @param userAddress 用户钱包地址
 * @param enabled 是否启用查询
 */
export function useTokenBalance(
  tokenAddress?: Address,
  userAddress?: Address,
  enabled: boolean = true
) {
  const { tokenStore } = useStore();

  const {
    data: balance,
    isLoading,
    error,
    refetch,
  } = useReadContracts({
    contracts: [
      {
        address: tokenAddress!,
        abi: ERC20_ABI,
        functionName: 'balanceOf',
        args: [userAddress!],
      },
    ],
    query: {
      enabled: enabled && !!userAddress && !!tokenAddress,
      refetchInterval: 30000,
      staleTime: 30000,
    },
  });

  // 当查询结果返回时，更新对应代币的余额
  useEffect(() => {
    if (balance && balance[0] && tokenAddress) {
      const result = balance[0];
      if (result.status === 'success' && result.result) {
        const token = tokenStore.tokenList.find((t) => t.address === tokenAddress);
        if (token) {
          const decimals = token.decimals || 18;
          tokenStore.updateTokenBalance(tokenAddress, result.result as bigint, decimals);
        }
      }
    }
  }, [balance, tokenAddress, tokenStore]);

  return {
    balance: balance?.[0],
    isLoading,
    error,
    refetch,
  };
}
