import "reflect-metadata";
import { makeAutoObservable } from "mobx";

export default class Token {
  id?: number;
  chainid?: number;
  dest_chainid?: number;
  address?: string;
  ctoken_address?: string;
  dest_address?: string;
  name?: string;
  decimals?: number;
  symbol?: string;
  logouri?: string;
  cashier?: string;
  quick_swap_from?: string;
  quick_swap?: string;
  token_only_dest_wrapper?: string;
  need_unwrapper?: boolean;
  is_popular?: boolean;
  is_depintoken?: boolean;
  is_wrapped?: boolean;
  createat?: string;
  updateat?: string;
  router_address?: string;
  usdc_dest_wrapper?: string;
  ctoken?: string;

  // 可观察的余额属性
  balance?: string;
  balanceLoading?: boolean;

  constructor(data?: Partial<Token>) {
    if (data) {
      Object.assign(this, data);
    }
    // 初始化余额相关属性
    this.balance = this.balance || "0";
    this.balanceLoading = this.balanceLoading || false;

    // 使属性可观察
    makeAutoObservable(this);
  }

  /**
   * 设置余额
   * @param balance 余额字符串
   */
  setBalance(balance: string) {
    this.balance = balance;
  }

  /**
   * 设置余额加载状态
   * @param loading 是否正在加载
   */
  setBalanceLoading(loading: boolean) {
    this.balanceLoading = loading;
  }

  /**
   * 获取格式化的余额
   * @returns 格式化后的余额字符串
   */
  get formattedBalance(): string {
    if (this.balanceLoading) {
      return "...";
    }
    if (!this.balance || this.balance === "0") {
      return "0";
    }
    // 简单的格式化，可以根据需要扩展
    const num = parseFloat(this.balance);
    if (num < 0.0001 && num > 0) {
      return "< 0.0001";
    }
    return num.toFixed(4).replace(/\.?0+$/, "");
  }
}
