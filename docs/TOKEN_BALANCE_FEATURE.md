# 代币余额批量查询功能

本文档介绍了新添加的代币余额批量查询功能，该功能允许批量查询 tokenList 中所有代币的余额，并将余额信息设置到 Token 类的可观察 balance 属性中。

## 功能概述

- ✅ 批量查询多个代币的余额
- ✅ 余额信息存储在 Token 类的可观察属性中
- ✅ 自动刷新机制
- ✅ 加载状态管理
- ✅ 错误处理
- ✅ 格式化余额显示

## 核心组件

### 1. Token 类增强 (`src/types/token.ts`)

Token 类现在包含以下新属性：

```typescript
class Token {
  // 新增的可观察属性
  balance?: string;           // 余额字符串
  balanceLoading?: boolean;   // 加载状态

  // 新增的方法
  setBalance(balance: string): void
  setBalanceLoading(loading: boolean): void
  
  // 计算属性
  get formattedBalance(): string  // 格式化的余额显示
}
```

### 2. ERC20 ABI 常量 (`src/constants/abi.ts`)

定义了标准的 ERC20 合约 ABI，包含：
- `balanceOf` - 查询余额
- `decimals` - 获取精度
- `name`, `symbol`, `totalSupply` 等其他常用函数

### 3. TokenStore 增强 (`src/store/tokenStore.ts`)

新增方法：
- `getTokensOnChain(chainId: number)` - 获取指定链上的代币
- `updateTokenBalance()` - 更新单个代币余额
- `updateBatchBalances()` - 批量更新代币余额

### 4. 自定义 Hook (`src/hooks/useTokenBalances.ts`)

提供两个主要 Hook：

#### `useTokenBalances`
批量查询当前链上所有代币的余额：

```typescript
const {
  balanceResults,
  isLoading,
  error,
  refetch,
  isRefetching,
  tokensCount,
} = useTokenBalances(
  userAddress,    // 用户钱包地址
  chainId,        // 当前链ID
  enabled,        // 是否启用查询
  refetchInterval // 自动刷新间隔
);
```

#### `useTokenBalance`
查询单个代币的余额：

```typescript
const {
  balance,
  isLoading,
  error,
  refetch,
} = useTokenBalance(
  tokenAddress,   // 代币合约地址
  userAddress,    // 用户钱包地址
  enabled         // 是否启用查询
);
```

## 使用示例

### 1. 在组件中使用批量余额查询

```typescript
import { useTokenBalances } from '@/hooks/useTokenBalances';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';

const MyComponent = observer(() => {
  const { address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  // 批量查询余额
  const { isLoading, error } = useTokenBalances(
    address as Address,
    chainId,
    !!address && !!chainId,
    30000 // 30秒自动刷新
  );

  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
});
```

### 2. 显示代币余额

```typescript
const TokenItem = observer(({ token }) => {
  return (
    <div>
      <span>{token.symbol}</span>
      <span>
        {token.balanceLoading ? '加载中...' : token.formattedBalance}
      </span>
    </div>
  );
});
```

### 3. 使用现成的组件

```typescript
import TokenBalanceList from '@/components/TokenBalanceList';

const MyPage = () => {
  return (
    <div>
      <h1>我的代币</h1>
      <TokenBalanceList />
    </div>
  );
};
```

## 集成到现有组件

### Bridge 组件集成

Bridge 组件已经集成了余额查询功能：

```typescript
// 自动查询当前链上所有代币的余额
const { isLoading: balancesLoading } = useTokenBalances(
  address as Address,
  depositStore.fromNetwork.chainId,
  !!address && !!depositStore.fromNetwork.chainId,
  30000
);

// 显示选中代币的余额
<div>
  Balance: {depositStore.selectedToken?.balanceLoading 
    ? "..." 
    : depositStore.selectedToken?.formattedBalance || "0"
  } {depositStore.selectedToken?.symbol}
</div>
```

## 配置选项

### 自动刷新间隔
默认 30 秒，可以根据需要调整：

```typescript
useTokenBalances(address, chainId, true, 60000); // 60秒刷新
```

### 缓存策略
使用 wagmi 的内置缓存，`staleTime` 设置为 30 秒。

### 错误重试
自动重试 3 次，重试间隔递增：
- 第1次：1秒后重试
- 第2次：2秒后重试  
- 第3次：4秒后重试

## 注意事项

1. **网络切换**：当用户切换网络时，会自动查询新网络上的代币余额
2. **钱包连接**：只有在钱包连接后才会开始查询余额
3. **性能优化**：使用 MobX 的可观察属性，只有余额变化时才会触发组件重新渲染
4. **错误处理**：查询失败时会显示错误信息，不会影响其他功能

## 示例页面

查看 `src/examples/TokenBalanceExample.tsx` 获取完整的使用示例。

## 故障排除

### 余额不显示
1. 检查钱包是否已连接
2. 检查当前网络是否正确
3. 检查代币是否有有效的合约地址

### 查询失败
1. 检查网络连接
2. 检查 RPC 节点是否正常
3. 查看浏览器控制台的错误信息

### 性能问题
1. 减少自动刷新频率
2. 限制同时查询的代币数量
3. 使用 React.memo 优化组件渲染
